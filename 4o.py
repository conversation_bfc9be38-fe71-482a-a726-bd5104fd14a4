from fastapi import FastAPI, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List
import os
from dotenv import load_dotenv
from openai import OpenAI
from openai import APIConnectionError, APIError, RateLimitError, AuthenticationError
import base64
from io import BytesIO
from PIL import Image
import logging

# 加载环境变量
load_dotenv()
api_key = os.getenv("OPENAI_API_KEY")

if not api_key:
    raise ValueError("OPENAI_API_KEY environment variable is not set. Please check your .env file.")

# 初始化FastAPI应用
app = FastAPI(title="OpenAI Image Generation API")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化OpenAI客户端
client = OpenAI(
    api_key=api_key,
    timeout=60.0,  # 增加超时时间
    max_retries=1  # 增加重试次数
)

class ImageRequest(BaseModel):
    prompt: str
    model: Optional[str] = "gpt-image-1"
    n: Optional[int] = 1
    quality: Optional[str] = "high"  # "low", "medium", "high"
    size: Optional[str] = "1024x1024"  # "1024x1024", "1024x1536", "1536x1024"
    user: Optional[str] = None  # 用户标识符

class ImageEditRequest(BaseModel):
    prompt: str
    model: Optional[str] = "gpt-image-1"
    n: Optional[int] = 1
    size: Optional[str] = "1024x1024"  # "1024x1024", "1024x1536", "1536x1024"
    quality: Optional[str] = "high"  # "low", "medium", "high"
    user: Optional[str] = None  # 用户标识符

@app.get("/test-connection")
async def test_connection():
    """
    测试与 OpenAI API 的连接
    """
    try:
        # 尝试获取模型列表来测试连接
        models = client.models.list()
        return {
            "status": "success",
            "message": "成功连接到 OpenAI API",
            "models_count": len(models.data)
        }
    except APIConnectionError as e:
        return {
            "status": "connection_error",
            "message": f"无法连接到 OpenAI API: {str(e)}",
            "suggestion": "请检查网络连接、代理设置或防火墙配置"
        }
    except AuthenticationError as e:
        return {
            "status": "auth_error",
            "message": "API 密钥验证失败",
            "suggestion": "请检查 .env 文件中的 OPENAI_API_KEY 是否正确"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"测试连接时发生错误: {str(e)}"
        }

@app.post("/generate-image")
async def generate_image(request: ImageRequest):
    """
    根据文本提示生成图像
    """
    try:
        response = client.images.generate(
            model=request.model,
            prompt=request.prompt,
            n=request.n,
            quality=request.quality,
            size=request.size,
            output_format=request.output_format,
            output_compression=request.output_compression,
            background=request.background
        )

        # 处理返回的图像数据
        images = []
        for image_data in response.data:
            if hasattr(image_data, 'b64_json'):
                images.append(image_data.b64_json)
            else:
                images.append(image_data.url)

        return {"images": images}
    except APIConnectionError as e:
        logging.error(f"Connection error: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"无法连接到 OpenAI API。请检查网络连接或稍后重试。错误详情: {str(e)}"
        )
    except AuthenticationError as e:
        logging.error(f"Authentication error: {e}")
        raise HTTPException(
            status_code=401,
            detail="API 密钥无效。请检查您的 OpenAI API 密钥。"
        )
    except RateLimitError as e:
        logging.error(f"Rate limit error: {e}")
        raise HTTPException(
            status_code=429,
            detail="API 请求频率超限。请稍后重试。"
        )
    except APIError as e:
        logging.error(f"API error: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"OpenAI API 错误: {str(e)}"
        )
    except Exception as e:
        logging.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail=f"未知错误: {str(e)}")

@app.post("/edit-image")
async def edit_image(
    prompt: str,
    image: UploadFile = File(...),
    mask: Optional[UploadFile] = File(None),
    model: str = "gpt-image-1",
    n: int = 1,
    size: str = "auto",
    output_format: str = "jpeg",
    output_compression: int = 80,
    background: Optional[str] = None
):
    """
    编辑现有图像
    """
    try:
        image_content = await image.read()
        mask_content = await mask.read() if mask else None

        response = client.images.edit(
            model=model,
            image=image_content,
            mask=mask_content,
            prompt=prompt,
            n=n,
            size=size,
            output_format=output_format,
            output_compression=output_compression,
            background=background
        )

        # 处理返回的图像数据
        images = []
        for image_data in response.data:
            if hasattr(image_data, 'b64_json'):
                images.append(image_data.b64_json)
            else:
                images.append(image_data.url)

        return {"images": images}
    except Exception as e:
        logging.error(f"Edit image error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/inpainting")
async def inpainting(
    prompt: str,
    image: UploadFile = File(...),
    mask: UploadFile = File(...),
    model: str = "gpt-image-1",
    n: int = 1,
    size: str = "auto",
    output_format: str = "jpeg",
    output_compression: int = 80,
    background: Optional[str] = None
):
    """
    图像修复（inpainting）
    """
    try:
        image_content = await image.read()
        mask_content = await mask.read()

        response = client.images.edit(
            model=model,
            image=image_content,
            mask=mask_content,
            prompt=prompt,
            n=n,
            size=size,
            output_format=output_format,
            output_compression=output_compression,
            background=background
        )

        # 处理返回的图像数据
        images = []
        for image_data in response.data:
            if hasattr(image_data, 'b64_json'):
                images.append(image_data.b64_json)
            else:
                images.append(image_data.url)

        return {"images": images}
    except Exception as e:
        logging.error(f"Inpainting error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)