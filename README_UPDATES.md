# OpenAI 图像API更新说明

## 更新概述

根据OpenAI官方文档（https://platform.openai.com/docs/api-reference/images），本次更新主要针对`gpt-image-1`模型进行了优化和修正。

## 主要修改内容

### 1. 后端API修改 (4o.py)

#### 修正的参数：
- **尺寸 (size)**: 
  - 支持: `"1024x1024"`, `"1024x1536"`, `"1536x1024"`
  - 移除了不支持的尺寸选项

- **质量 (quality)**: 
  - 支持: `"low"`, `"medium"`, `"high"`
  - 默认值: `"high"`

- **响应格式**: 
  - `gpt-image-1`总是返回base64编码的图像
  - 自动设置`response_format: "b64_json"`

#### 移除的参数：
- `output_format` - gpt-image-1不支持
- `output_compression` - gpt-image-1不支持  
- `background` - gpt-image-1不支持

#### 新增的参数：
- `user` - 用户标识符，用于监控和滥用检测

#### API端点变更：
- 保留: `/generate-image` - 图像生成
- 保留: `/edit-image` - 图像编辑（支持mask）
- 移除: `/inpainting` - 与edit-image功能重复

### 2. 前端界面修改 (image-generator.html)

#### 界面优化：
- 移除了"图像修复"标签页
- 更新了尺寸选项，符合gpt-image-1规范
- 添加了质量选择器
- 改进了错误处理

#### 新增功能：
- 图像下载按钮
- 更好的base64图像显示支持
- 改进的表单验证

## 支持的功能

### 1. 图像生成
- **模型**: gpt-image-1
- **尺寸**: 1024x1024 (正方形), 1024x1536 (竖版), 1536x1024 (横版)
- **质量**: 低/中/高
- **输出**: base64编码的PNG图像

### 2. 图像编辑
- **模型**: gpt-image-1
- **输入**: PNG/JPG图像文件 (< 20MB)
- **蒙版**: 可选的PNG蒙版文件
- **输出**: base64编码的PNG图像

## 使用方法

### 启动服务器
```bash
python 4o.py
```

### 访问界面
打开浏览器访问: `image-generator.html`

### API测试
```bash
python test_api.py
```

## API示例

### 生成图像
```python
import requests

response = requests.post('http://localhost:8000/generate-image', json={
    "prompt": "A beautiful sunset over mountains",
    "model": "gpt-image-1",
    "size": "1024x1024",
    "quality": "high",
    "n": 1
})

result = response.json()
images = result["images"]  # base64编码的图像数据
```

### 编辑图像
```python
import requests

with open("image.png", "rb") as f:
    files = {"image": ("image.png", f, "image/png")}
    data = {
        "prompt": "Add a rainbow",
        "model": "gpt-image-1",
        "size": "1024x1024",
        "quality": "high"
    }
    
    response = requests.post('http://localhost:8000/edit-image', 
                           files=files, data=data)

result = response.json()
images = result["images"]  # base64编码的图像数据
```

## 注意事项

1. **API密钥**: 确保在`.env`文件中设置了有效的`OPENAI_API_KEY`
2. **模型访问**: `gpt-image-1`可能需要特殊访问权限
3. **图像格式**: 输出图像为PNG格式的base64编码
4. **文件大小**: 上传的图像文件必须小于20MB
5. **网络**: 确保网络连接正常，API调用可能需要较长时间

## 错误处理

API包含完整的错误处理机制：
- 连接错误 (503)
- 认证错误 (401) 
- 频率限制错误 (429)
- API错误 (500)

## 测试

运行测试脚本验证功能：
```bash
python test_api.py
```

测试将验证：
- API连接
- 图像生成
- 图像编辑

## 兼容性

- **Python**: 3.7+
- **依赖**: FastAPI, OpenAI, Pillow, python-dotenv
- **浏览器**: 现代浏览器支持HTML5和JavaScript ES6+
