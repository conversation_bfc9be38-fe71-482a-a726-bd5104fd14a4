#!/usr/bin/env python3
"""
测试脚本：验证修改后的OpenAI图像API是否正常工作
"""

import requests
import json
import base64
from io import BytesIO
from PIL import Image
import os

# API基础URL
BASE_URL = "http://localhost:8000"

def test_connection():
    """测试API连接"""
    print("测试API连接...")
    try:
        response = requests.get(f"{BASE_URL}/test-connection")
        result = response.json()
        print(f"连接测试结果: {result}")
        return result.get("status") == "success"
    except Exception as e:
        print(f"连接测试失败: {e}")
        return False

def test_image_generation():
    """测试图像生成"""
    print("\n测试图像生成...")
    
    payload = {
        "prompt": "A beautiful sunset over mountains, digital art",
        "model": "gpt-image-1",
        "size": "1024x1024",
        "quality": "high",
        "n": 1
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/generate-image",
            headers={"Content-Type": "application/json"},
            json=payload
        )
        
        if response.status_code == 200:
            result = response.json()
            images = result.get("images", [])
            print(f"成功生成 {len(images)} 张图像")
            
            # 保存第一张图像
            if images:
                image_data = images[0]
                if image_data.startswith("data:image/png;base64,"):
                    base64_data = image_data.split(",")[1]
                    image_bytes = base64.b64decode(base64_data)
                    
                    # 保存图像
                    with open("test_generated_image.png", "wb") as f:
                        f.write(image_bytes)
                    print("图像已保存为 test_generated_image.png")
                    return True
        else:
            print(f"生成失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"生成图像时出错: {e}")
        return False

def test_image_edit():
    """测试图像编辑（需要先有一张图像）"""
    print("\n测试图像编辑...")
    
    # 检查是否有测试图像
    if not os.path.exists("test_generated_image.png"):
        print("没有找到测试图像，跳过编辑测试")
        return False
    
    try:
        # 准备表单数据
        with open("test_generated_image.png", "rb") as f:
            files = {
                "image": ("test_image.png", f, "image/png")
            }
            data = {
                "prompt": "Add a rainbow in the sky",
                "model": "gpt-image-1",
                "size": "1024x1024",
                "quality": "high",
                "n": "1"
            }
            
            response = requests.post(
                f"{BASE_URL}/edit-image",
                files=files,
                data=data
            )
        
        if response.status_code == 200:
            result = response.json()
            images = result.get("images", [])
            print(f"成功编辑图像，生成 {len(images)} 张图像")
            
            # 保存编辑后的图像
            if images:
                image_data = images[0]
                if image_data.startswith("data:image/png;base64,"):
                    base64_data = image_data.split(",")[1]
                    image_bytes = base64.b64decode(base64_data)
                    
                    with open("test_edited_image.png", "wb") as f:
                        f.write(image_bytes)
                    print("编辑后的图像已保存为 test_edited_image.png")
                    return True
        else:
            print(f"编辑失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"编辑图像时出错: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试OpenAI图像API...")
    
    # 测试连接
    if not test_connection():
        print("API连接失败，请确保服务器正在运行")
        return
    
    # 测试图像生成
    if test_image_generation():
        print("✓ 图像生成测试通过")
    else:
        print("✗ 图像生成测试失败")
    
    # 测试图像编辑
    if test_image_edit():
        print("✓ 图像编辑测试通过")
    else:
        print("✗ 图像编辑测试失败")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
