<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 图像生成工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab-button {
            padding: 10px 20px;
            background: #f0f0f0;
            border: none;
            cursor: pointer;
            flex: 1;
            font-size: 16px;
        }
        .tab-button.active {
            background: #007bff;
            color: white;
        }
        .tab-button:first-child {
            border-radius: 4px 0 0 4px;
        }
        .tab-button:last-child {
            border-radius: 0 4px 4px 0;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        textarea, select, input[type="file"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            min-height: 100px;
            resize: vertical;
        }
        button[type="submit"] {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button[type="submit"]:hover {
            background: #0069d9;
        }
        button[type="submit"]:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 30px;
        }
        .image-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .image-item {
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            background: white;
        }
        .image-item img {
            max-width: 100%;
            height: auto;
            display: block;
        }
        .loading {
            text-align: center;
            padding: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI 图像生成工具</h1>
        
        <div class="tabs">
            <button class="tab-button active" data-tab="generate">生成图像</button>
            <button class="tab-button" data-tab="edit">编辑图像</button>
            <button class="tab-button" data-tab="inpaint">图像修复</button>
        </div>
        
        <div id="generate" class="tab-content active">
            <form id="generate-form">
                <div class="form-group">
                    <label for="generate-prompt">提示词:</label>
                    <textarea id="generate-prompt" required></textarea>
                </div>
                
                <div class="form-group">
                    <label for="generate-size">尺寸:</label>
                    <select id="generate-size">
                        <option value="1024x1024">1024x1024</option>
                        <option value="512x512">512x512</option>
                        <option value="256x256">256x256</option>
                    </select>
                </div>
                
                <button type="submit" id="generate-button">生成图像</button>
            </form>
        </div>
        
        <div id="edit" class="tab-content">
            <form id="edit-form">
                <div class="form-group">
                    <label for="edit-prompt">提示词:</label>
                    <textarea id="edit-prompt" required></textarea>
                </div>
                
                <div class="form-group">
                    <label for="edit-image">上传图像:</label>
                    <input type="file" id="edit-image" required>
                </div>
                
                <div class="form-group">
                    <label for="edit-mask">上传蒙版 (可选):</label>
                    <input type="file" id="edit-mask">
                </div>
                
                <div class="form-group">
                    <label for="edit-size">尺寸:</label>
                    <select id="edit-size">
                        <option value="1024x1024">1024x1024</option>
                        <option value="512x512">512x512</option>
                        <option value="256x256">256x256</option>
                    </select>
                </div>
                
                <button type="submit" id="edit-button">编辑图像</button>
            </form>
        </div>
        
        <div id="inpaint" class="tab-content">
            <form id="inpaint-form">
                <div class="form-group">
                    <label for="inpaint-prompt">提示词:</label>
                    <textarea id="inpaint-prompt" required></textarea>
                </div>
                
                <div class="form-group">
                    <label for="inpaint-image">上传图像:</label>
                    <input type="file" id="inpaint-image" required>
                </div>
                
                <div class="form-group">
                    <label for="inpaint-mask">上传蒙版:</label>
                    <input type="file" id="inpaint-mask" required>
                </div>
                
                <div class="form-group">
                    <label for="inpaint-size">尺寸:</label>
                    <select id="inpaint-size">
                        <option value="1024x1024">1024x1024</option>
                        <option value="512x512">512x512</option>
                        <option value="256x256">256x256</option>
                    </select>
                </div>
                
                <button type="submit" id="inpaint-button">修复图像</button>
            </form>
        </div>
        
        <div class="loading" id="loading">
            <p>处理中，请稍候...</p>
        </div>
        
        <div class="results" id="results" style="display: none;">
            <h2>生成结果</h2>
            <div class="image-grid" id="image-grid"></div>
        </div>
    </div>

    <script>
        // 切换标签页
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', () => {
                // 移除所有活动状态
                document.querySelectorAll('.tab-button').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                
                // 添加活动状态到当前标签
                button.classList.add('active');
                const tabId = button.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });
        
        // 生成图像表单提交
        document.getElementById('generate-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            const prompt = document.getElementById('generate-prompt').value;
            const size = document.getElementById('generate-size').value;
            
            if (!prompt) return;
            
            showLoading(true);
            clearResults();
            
            try {
                const response = await fetch('http://localhost:8000/generate-image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt,
                        model: "dall-e-3", // 使用dall-e-3模型
                        size,
                        quality: "standard",
                        response_format: "url"
                    }),
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '请求失败');
                }
                
                const data = await response.json();
                displayResults(data.images);
            } catch (error) {
                console.error('Error generating image:', error);
                alert(`生成图像时出错: ${error.message}`);
            } finally {
                showLoading(false);
            }
        });
        
        // 编辑图像表单提交
        document.getElementById('edit-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            const prompt = document.getElementById('edit-prompt').value;
            const imageFile = document.getElementById('edit-image').files[0];
            const maskFile = document.getElementById('edit-mask').files[0];
            const size = document.getElementById('edit-size').value;
            
            if (!prompt || !imageFile) return;
            
            showLoading(true);
            clearResults();
            
            const formData = new FormData();
            formData.append('prompt', prompt);
            formData.append('image', imageFile);
            if (maskFile) formData.append('mask', maskFile);
            formData.append('size', size);
            
            try {
                const response = await fetch('http://localhost:8000/edit-image', {
                    method: 'POST',
                    body: formData,
                });
                
                const data = await response.json();
                displayResults(data.images);
            } catch (error) {
                console.error('Error editing image:', error);
                alert('编辑图像时出错，请查看控制台获取详细信息。');
            } finally {
                showLoading(false);
            }
        });
        
        // 图像修复表单提交
        document.getElementById('inpaint-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            const prompt = document.getElementById('inpaint-prompt').value;
            const imageFile = document.getElementById('inpaint-image').files[0];
            const maskFile = document.getElementById('inpaint-mask').files[0];
            const size = document.getElementById('inpaint-size').value;
            
            if (!prompt || !imageFile || !maskFile) return;
            
            showLoading(true);
            clearResults();
            
            const formData = new FormData();
            formData.append('prompt', prompt);
            formData.append('image', imageFile);
            formData.append('mask', maskFile);
            formData.append('size', size);
            
            try {
                const response = await fetch('http://localhost:8000/inpainting', {
                    method: 'POST',
                    body: formData,
                });
                
                const data = await response.json();
                displayResults(data.images);
            } catch (error) {
                console.error('Error inpainting:', error);
                alert('图像修复时出错，请查看控制台获取详细信息。');
            } finally {
                showLoading(false);
            }
        });
        
        // 显示加载状态
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
            document.getElementById('generate-button').disabled = show;
            document.getElementById('edit-button').disabled = show;
            document.getElementById('inpaint-button').disabled = show;
        }
        
        // 清除结果
        function clearResults() {
            document.getElementById('image-grid').innerHTML = '';
            document.getElementById('results').style.display = 'none';
        }
        
        // 显示结果
        function displayResults(images) {
            const grid = document.getElementById('image-grid');
            grid.innerHTML = '';
            
            if (images && images.length > 0) {
                images.forEach((image, index) => {
                    const item = document.createElement('div');
                    item.className = 'image-item';
                    
                    const img = document.createElement('img');
                    img.src = image;
                    img.alt = `Generated ${index + 1}`;
                    
                    item.appendChild(img);
                    grid.appendChild(item);
                });
                
                document.getElementById('results').style.display = 'block';
            }
        }
    </script>
</body>
</html>